{% extends "admin/base.html" %}

{% block title %}استرداد الدفع {{ payment.payment_number }} - {{ super() }}{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto">
    <!-- Page Header -->
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900">
            <i class="fas fa-undo {{ 'ml-3' if current_lang == 'ar' else 'mr-3' }}"></i>
            استرداد الدفع
        </h1>
        <a href="{{ url_for('admin.order_detail', id=payment.order.id) }}" class="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors">
            <i class="fas fa-arrow-left {{ 'ml-2' if current_lang == 'ar' else 'mr-2' }}"></i>
            العودة للطلب
        </a>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Payment Summary -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="bg-blue-500 text-white px-6 py-4">
                <h3 class="text-lg font-semibold">
                    <i class="fas fa-receipt {{ 'ml-2' if current_lang == 'ar' else 'mr-2' }}"></i>
                    معلومات الدفع
                </h3>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    <div class="flex justify-between">
                        <span class="font-medium text-gray-700">رقم الدفع:</span>
                        <span class="text-gray-900">{{ payment.payment_number }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="font-medium text-gray-700">رقم الطلب:</span>
                        <span class="text-gray-900">{{ payment.order.order_number }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="font-medium text-gray-700">العميل:</span>
                        <span class="text-gray-900">{{ payment.order.customer_name }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="font-medium text-gray-700">طريقة الدفع:</span>
                        <span class="text-gray-900">{{ payment.get_payment_method_text('ar') }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="font-medium text-gray-700">تاريخ الدفع:</span>
                        <span class="text-gray-900">{{ payment.payment_timestamp.strftime('%Y-%m-%d %H:%M') }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="font-medium text-gray-700">المعالج بواسطة:</span>
                        <span class="text-gray-900">{{ payment.processed_by.username }}</span>
                    </div>
                    {% if payment.reference_number %}
                        <div class="flex justify-between">
                            <span class="font-medium text-gray-700">رقم المرجع:</span>
                            <span class="text-gray-900">{{ payment.reference_number }}</span>
                        </div>
                    {% endif %}
                </div>

                <div class="mt-6 pt-6 border-t border-gray-200">
                    <div class="flex justify-between text-lg font-bold">
                        <span>مبلغ الدفع:</span>
                        <span class="text-green-600">{{ payment.get_formatted_amount() }}</span>
                    </div>
                </div>

                <!-- Payment Status -->
                <div class="mt-4">
                    <span class="font-medium text-gray-700">حالة الدفع:</span>
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {{ 'mr-2' if current_lang == 'ar' else 'ml-2' }}
                        {% if payment.get_status_color() == 'green' %}bg-green-100 text-green-800
                        {% elif payment.get_status_color() == 'red' %}bg-red-100 text-red-800
                        {% elif payment.get_status_color() == 'yellow' %}bg-yellow-100 text-yellow-800
                        {% elif payment.get_status_color() == 'blue' %}bg-blue-100 text-blue-800
                        {% else %}bg-gray-100 text-gray-800{% endif %}">
                        {{ payment.get_status_text('ar') }}
                    </span>
                </div>

                {% if payment.notes %}
                    <div class="mt-6 pt-6 border-t border-gray-200">
                        <h4 class="text-md font-semibold text-gray-900 mb-2">ملاحظات الدفع:</h4>
                        <p class="text-gray-700 text-sm">{{ payment.notes }}</p>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Refund Form -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="bg-red-500 text-white px-6 py-4">
                <h3 class="text-lg font-semibold">
                    <i class="fas fa-money-bill-wave {{ 'ml-2' if current_lang == 'ar' else 'mr-2' }}"></i>
                    معلومات الاسترداد
                </h3>
            </div>
            <div class="p-6">
                <form method="POST">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                    
                    <!-- Refund Amount -->
                    <div class="mb-6">
                        <label for="amount" class="block text-sm font-medium text-gray-700 mb-2">
                            مبلغ الاسترداد <span class="text-red-500">*</span>
                        </label>
                        <div class="relative">
                            {{ form.amount(class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent", id="amount", placeholder="0.00") }}
                            <div class="absolute inset-y-0 {{ 'left-0' if current_lang == 'ar' else 'right-0' }} pr-3 flex items-center pointer-events-none">
                                <span class="text-gray-500 sm:text-sm">{{ payment.order.currency }}</span>
                            </div>
                        </div>
                        {% if form.amount.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {% for error in form.amount.errors %}
                                    <p>{{ error }}</p>
                                {% endfor %}
                            </div>
                        {% endif %}
                        <p class="mt-1 text-sm text-gray-500">
                            الحد الأقصى: {{ payment.get_formatted_amount() }}
                        </p>
                    </div>

                    <!-- Refund Reason -->
                    <div class="mb-6">
                        <label for="reason" class="block text-sm font-medium text-gray-700 mb-2">
                            سبب الاسترداد <span class="text-red-500">*</span>
                        </label>
                        {{ form.reason(class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent", id="reason", placeholder="اذكر سبب الاسترداد") }}
                        {% if form.reason.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {% for error in form.reason.errors %}
                                    <p>{{ error }}</p>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <!-- Refund Type -->
                    <div class="mb-6 p-4 bg-gray-50 rounded-lg">
                        <h4 class="text-sm font-medium text-gray-900 mb-3">نوع الاسترداد:</h4>
                        <div class="space-y-2 text-sm">
                            <div class="flex items-center">
                                <input type="radio" id="full_refund" name="refund_type" value="full" 
                                       class="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300">
                                <label for="full_refund" class="{{ 'mr-3' if current_lang == 'ar' else 'ml-3' }} text-gray-700">
                                    استرداد كامل ({{ payment.get_formatted_amount() }})
                                </label>
                            </div>
                            <div class="flex items-center">
                                <input type="radio" id="partial_refund" name="refund_type" value="partial" checked
                                       class="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300">
                                <label for="partial_refund" class="{{ 'mr-3' if current_lang == 'ar' else 'ml-3' }} text-gray-700">
                                    استرداد جزئي (حسب المبلغ المدخل)
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="flex space-x-4 {{ 'space-x-reverse' if current_lang == 'ar' else '' }}">
                        <button type="submit" 
                                class="flex-1 bg-red-500 text-white py-3 px-4 rounded-lg font-medium hover:bg-red-600 transition-colors focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2">
                            <i class="fas fa-undo {{ 'ml-2' if current_lang == 'ar' else 'mr-2' }}"></i>
                            تأكيد الاسترداد
                        </button>
                        
                        <a href="{{ url_for('admin.order_detail', id=payment.order.id) }}" 
                           class="flex-1 bg-gray-500 text-white py-3 px-4 rounded-lg font-medium hover:bg-gray-600 transition-colors text-center">
                            <i class="fas fa-times {{ 'ml-2' if current_lang == 'ar' else 'mr-2' }}"></i>
                            إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Warning Notice -->
    <div class="mt-6 bg-red-50 border border-red-200 rounded-lg p-4">
        <div class="flex">
            <div class="flex-shrink-0">
                <i class="fas fa-exclamation-triangle text-red-600"></i>
            </div>
            <div class="{{ 'mr-3' if current_lang == 'ar' else 'ml-3' }}">
                <h3 class="text-sm font-medium text-red-800">تحذير مهم</h3>
                <div class="mt-2 text-sm text-red-700">
                    <ul class="list-disc {{ 'pr-5' if current_lang == 'ar' else 'pl-5' }} space-y-1">
                        <li>تأكد من صحة مبلغ الاسترداد قبل التأكيد</li>
                        <li>سيتم خصم المبلغ من رصيد الخزنة إذا كان الدفع نقدياً</li>
                        <li>لا يمكن التراجع عن الاسترداد بعد التأكيد</li>
                        <li>سيتم إنشاء سجل دفع سالب للاسترداد</li>
                        <li>تأكد من توفر المبلغ في الخزنة قبل الاسترداد النقدي</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Order Summary -->
    <div class="mt-8 bg-white rounded-lg shadow-md overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">
                <i class="fas fa-shopping-cart {{ 'ml-2' if current_lang == 'ar' else 'mr-2' }}"></i>
                ملخص الطلب
            </h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h4 class="text-md font-semibold text-gray-900 mb-3">عناصر الطلب:</h4>
                    <div class="space-y-2">
                        {% for item in payment.order.order_items %}
                            <div class="flex justify-between text-sm">
                                <span>{{ item.product.get_name('ar') }} × {{ item.quantity }}</span>
                                <span>{{ item.get_formatted_total_price() }}</span>
                            </div>
                        {% endfor %}
                    </div>
                </div>
                <div>
                    <h4 class="text-md font-semibold text-gray-900 mb-3">معلومات الطلب:</h4>
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-600">الطاولة:</span>
                            <span>طاولة {{ payment.order.table.table_number }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">إجمالي الطلب:</span>
                            <span class="font-medium">{{ payment.order.get_formatted_total() }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">حالة الدفع:</span>
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                {% if payment.order.payment_status == 'unpaid' %}bg-red-100 text-red-800
                                {% elif payment.order.payment_status == 'partial' %}bg-yellow-100 text-yellow-800
                                {% elif payment.order.payment_status == 'paid' %}bg-green-100 text-green-800
                                {% else %}bg-gray-100 text-gray-800{% endif %}">
                                {% if payment.order.payment_status == 'unpaid' %}غير مدفوع
                                {% elif payment.order.payment_status == 'partial' %}مدفوع جزئياً
                                {% elif payment.order.payment_status == 'paid' %}مدفوع
                                {% else %}{{ payment.order.payment_status }}{% endif %}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Handle refund type selection
document.querySelectorAll('input[name="refund_type"]').forEach(function(radio) {
    radio.addEventListener('change', function() {
        const amountField = document.getElementById('amount');
        if (this.value === 'full') {
            amountField.value = {{ payment.amount }};
            amountField.readOnly = true;
            amountField.classList.add('bg-gray-100');
        } else {
            amountField.readOnly = false;
            amountField.classList.remove('bg-gray-100');
            amountField.focus();
        }
    });
});

// Format number input
document.getElementById('amount').addEventListener('input', function() {
    let value = this.value.replace(/[^\d.]/g, '');
    if (value.split('.').length > 2) {
        value = value.substring(0, value.lastIndexOf('.'));
    }
    
    // Limit to payment amount
    const maxAmount = {{ payment.amount }};
    if (parseFloat(value) > maxAmount) {
        value = maxAmount.toString();
    }
    
    this.value = value;
});

// Auto-focus on amount field
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('amount').focus();
});
</script>
{% endblock %}
