{% extends "admin/base.html" %}

{% block title %}قائمة الورديات - {{ super() }}{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto">
    <!-- Page Header -->
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900">
            <i class="fas fa-history {{ 'ml-3' if current_lang == 'ar' else 'mr-3' }}"></i>
            قائمة الورديات
        </h1>
        <div class="flex space-x-2 {{ 'space-x-reverse' if current_lang == 'ar' else '' }}">
            <a href="{{ url_for('admin.pos_start_shift') }}" class="bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-colors">
                <i class="fas fa-plus {{ 'ml-2' if current_lang == 'ar' else 'mr-2' }}"></i>
                وردية جديدة
            </a>
            <a href="{{ url_for('admin.pos_dashboard') }}" class="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors">
                <i class="fas fa-arrow-left {{ 'ml-2' if current_lang == 'ar' else 'mr-2' }}"></i>
                العودة لنقاط البيع
            </a>
        </div>
    </div>

    <!-- Shifts List -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
        {% if shifts.items %}
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 {{ 'text-right' if current_lang == 'ar' else 'text-left' }} text-xs font-medium text-gray-500 uppercase tracking-wider">رقم الوردية</th>
                            <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">التاريخ</th>
                            <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">المدة</th>
                            <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">الرصيد الافتتاحي</th>
                            <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">الرصيد الختامي</th>
                            <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">إجمالي المبيعات</th>
                            <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                            <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for shift in shifts.items %}
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">{{ shift.shift_number }}</div>
                                    <div class="text-sm text-gray-500">{{ shift.user.username }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-center">
                                    <div class="text-sm text-gray-900">{{ shift.start_time.strftime('%Y-%m-%d') }}</div>
                                    <div class="text-sm text-gray-500">{{ shift.start_time.strftime('%H:%M') }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-900">
                                    {{ shift.get_duration() }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-900">
                                    {{ shift.get_formatted_opening_balance() }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-900">
                                    {{ shift.get_formatted_closing_balance() }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-900">
                                    {{ shift.get_formatted_total_sales() }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-center">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                        {% if shift.status == 'active' %}bg-green-100 text-green-800
                                        {% elif shift.status == 'closed' %}bg-gray-100 text-gray-800
                                        {% else %}bg-yellow-100 text-yellow-800{% endif %}">
                                        {% if shift.status == 'active' %}نشطة
                                        {% elif shift.status == 'closed' %}مغلقة
                                        {% else %}{{ shift.status }}{% endif %}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
                                    <div class="flex justify-center space-x-2 {{ 'space-x-reverse' if current_lang == 'ar' else '' }}">
                                        <a href="{{ url_for('admin.pos_shift_report', shift_id=shift.id) }}"
                                           class="text-blue-600 hover:text-blue-900">
                                            <i class="fas fa-eye" title="عرض التقرير"></i>
                                        </a>
                                        {% if shift.status == 'active' and shift.user_id == current_user.id %}
                                            <a href="{{ url_for('admin.pos_close_shift') }}"
                                               class="text-red-600 hover:text-red-900">
                                                <i class="fas fa-stop" title="إغلاق الوردية"></i>
                                            </a>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if shifts.pages > 1 %}
                <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                    <div class="flex-1 flex justify-between sm:hidden">
                        {% if shifts.has_prev %}
                            <a href="{{ url_for('admin.pos_shifts_list', page=shifts.prev_num) }}" 
                               class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                السابق
                            </a>
                        {% endif %}
                        {% if shifts.has_next %}
                            <a href="{{ url_for('admin.pos_shifts_list', page=shifts.next_num) }}" 
                               class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                التالي
                            </a>
                        {% endif %}
                    </div>
                    <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                        <div>
                            <p class="text-sm text-gray-700">
                                عرض
                                <span class="font-medium">{{ shifts.per_page * (shifts.page - 1) + 1 }}</span>
                                إلى
                                <span class="font-medium">{{ shifts.per_page * (shifts.page - 1) + shifts.items|length }}</span>
                                من
                                <span class="font-medium">{{ shifts.total }}</span>
                                نتيجة
                            </p>
                        </div>
                        <div>
                            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                {% if shifts.has_prev %}
                                    <a href="{{ url_for('admin.pos_shifts_list', page=shifts.prev_num) }}" 
                                       class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                        <i class="fas fa-chevron-left"></i>
                                    </a>
                                {% endif %}
                                
                                {% for page_num in shifts.iter_pages() %}
                                    {% if page_num %}
                                        {% if page_num != shifts.page %}
                                            <a href="{{ url_for('admin.pos_shifts_list', page=page_num) }}" 
                                               class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                                {{ page_num }}
                                            </a>
                                        {% else %}
                                            <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600">
                                                {{ page_num }}
                                            </span>
                                        {% endif %}
                                    {% else %}
                                        <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                                            ...
                                        </span>
                                    {% endif %}
                                {% endfor %}
                                
                                {% if shifts.has_next %}
                                    <a href="{{ url_for('admin.pos_shifts_list', page=shifts.next_num) }}" 
                                       class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                        <i class="fas fa-chevron-right"></i>
                                    </a>
                                {% endif %}
                            </nav>
                        </div>
                    </div>
                </div>
            {% endif %}
        {% else %}
            <div class="p-6 text-center text-gray-500">
                <i class="fas fa-clock text-4xl mb-4"></i>
                <p class="text-lg mb-2">لا توجد ورديات</p>
                <p class="text-sm mb-4">لم يتم تسجيل أي ورديات بعد</p>
                <a href="{{ url_for('admin.pos_start_shift') }}" 
                   class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700">
                    <i class="fas fa-plus {{ 'ml-2' if current_lang == 'ar' else 'mr-2' }}"></i>
                    بدء وردية جديدة
                </a>
            </div>
        {% endif %}
    </div>

    <!-- Summary Statistics -->
    {% if shifts.items %}
        <div class="mt-8 grid grid-cols-1 md:grid-cols-4 gap-6">
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-clock text-blue-600 text-2xl"></i>
                    </div>
                    <div class="{{ 'mr-4' if current_lang == 'ar' else 'ml-4' }}">
                        <p class="text-sm font-medium text-gray-500">إجمالي الورديات</p>
                        <p class="text-2xl font-bold text-gray-900">{{ shifts.total }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-play text-green-600 text-2xl"></i>
                    </div>
                    <div class="{{ 'mr-4' if current_lang == 'ar' else 'ml-4' }}">
                        <p class="text-sm font-medium text-gray-500">الورديات النشطة</p>
                        <p class="text-2xl font-bold text-gray-900">
                            {{ shifts.items|selectattr('status', 'equalto', 'active')|list|length }}
                        </p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-stop text-gray-600 text-2xl"></i>
                    </div>
                    <div class="{{ 'mr-4' if current_lang == 'ar' else 'ml-4' }}">
                        <p class="text-sm font-medium text-gray-500">الورديات المغلقة</p>
                        <p class="text-2xl font-bold text-gray-900">
                            {{ shifts.items|selectattr('status', 'equalto', 'closed')|list|length }}
                        </p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-chart-line text-purple-600 text-2xl"></i>
                    </div>
                    <div class="{{ 'mr-4' if current_lang == 'ar' else 'ml-4' }}">
                        <p class="text-sm font-medium text-gray-500">إجمالي المبيعات</p>
                        <p class="text-2xl font-bold text-gray-900">
                            {{ (shifts.items|sum(attribute='total_sales') or 0) }} EGP
                        </p>
                    </div>
                </div>
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}
